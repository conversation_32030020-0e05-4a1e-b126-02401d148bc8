import { css } from "@emotion/react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useNavigate } from "@remix-run/react";
import { AppTheme, RDSButton, RDSTypography, RDSUploadFile } from "@roshn/ui-kit";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { Input } from "~/components/form-components/input/input";
import { Select } from "~/components/form-components/select/select";
import { useAppPath } from "~/hooks/use-app-path";
import { useInjection } from "~/hooks/use-di";
import { ProductService } from "~/services/product-list/product-list";
import { AppPaths } from "~/utils/app-paths";

type InputPayload = {
  [key: string]: string;
};

type CustomAttribute = {
  label: string;
  value: string;
};

export type OutputPayload = {
  title: string;
  description: string;
  weight_unit: string;
  weight: string;
  price: string;
  sale_price: string;
  categories: string[];
  images: string[];
  variants: string[];
  modifier_groups: string[];
  combinations: string[];
  is_popular: boolean;
  cross_sell_groups: string[];
  title_ar: string;
  marketplace_categories: number[];
  per_item_quantity: number;
  stock_quantity: string | null;
  sku: string;
  custom_attributes: CustomAttribute[];
  status: string;
  manage_stock: boolean;
};

const styles = {
  wrapper: (theme: AppTheme) =>
    css({
      paddingInline: theme.rds.dimension["800"],
      paddingBlock: theme.rds.dimension["400"],
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["400"],
      alignItems: "flex-start",
      backgroundColor: theme.rds.color.background.ui.secondary.default,
      minHeight: "100vh",
    }),

  button: (theme: AppTheme) =>
    css({
      textTransform: "none",
      width: "fit-content",
      paddingInlineStart: 0,

      "& svg": {
        color: `${theme.rds.color.text.brand.primary.default} !important`,
      },
    }),

  sectionWrapper: (theme: AppTheme) =>
    css({
      width: "100%",
      backgroundColor: theme.rds.color.background.ui.canvas,
      padding: theme.rds.dimension["300"],
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
      border: `1px solid ${theme.rds.color.border.ui.primary}`,
    }),

  sectionHeadingText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.heading.emphasis.h5,
      fontWeight: 500,
      color: theme.rds.color.text.ui.primary,
      textTransform: "uppercase",
    }),

  actionsHeadingText: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.heading.emphasis.h5,
      fontWeight: 500,
      color: theme.rds.color.text.ui.primary,
    }),

  infoHead: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.lg,
      color: theme.rds.color.text.ui.tertiary,
    }),

  infoDes: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.label.emphasis.lg,
      color: theme.rds.color.text.ui.primary,
    }),

  internalWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
      zIndex: 0,
    }),

  sectionChildrenWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["200"],
    }),

  sectionDivider: (theme: AppTheme) =>
    css({
      height: "1px",
      backgroundColor: theme.rds.color.border.ui.primary,
    }),

  form: css({
    flex: "0 0 70%",
  }),

  sectionsWrapper: (theme: AppTheme) =>
    css({
      display: "flex",
      gap: theme.rds.dimension["200"],
      height: "fit-content",
      width: "100%",
    }),

  infoTypoWrapper: css({
    display: "flex",
    justifyContent: "space-between",
  }),

  sectionLayout: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["400"],
    }),

  infoSections: css({
    flex: "0 0 30%",
  }),

  actionsLayout: (theme: AppTheme) =>
    css({
      display: "flex",
      flexDirection: "column",
      gap: theme.rds.dimension["100"],
    }),

  listingTypo: (theme: AppTheme) =>
    css({
      ...theme.rds.typographies.body.md,
      color: theme.rds.color.text.ui.tertiary,
    }),
};

const schema = z.object({
  images: z.any(),
  title: z.any(),
  description: z.any(),
  category: z.any(),
  price: z.any(),
  salePrice: z.any(),
  per_item_quantity: z.any(),
  communityId: z.any(),
  endDate: z.any(),
  eventCommunityAR: z.any(),
  eventCommunityEN: z.any(),
  eventContactNumber: z.any(),
  eventDate: z.any(),
  eventDescriptionAR: z.any(),
  eventDescriptionEN: z.any(),
  eventRegistrationStatus: z.any(),
  eventTermsAndConditionsAR: z.any(),
  eventTermsAndConditionsEN: z.any(),
  eventTitleEN: z.any(),
  eventTitleAR: z.any(),
  locationLink: z.any(),
  maxAttendees: z.any(),
  totalEventGuests: z.any(),
});

const Section = ({ heading, children }: { heading: string; children: React.ReactNode }) => {
  return (
    <div css={styles.sectionWrapper}>
      <RDSTypography css={styles.sectionHeadingText}>{heading}</RDSTypography>
      <div css={styles.sectionDivider} />
      <div css={styles.sectionChildrenWrapper}>{children}</div>
    </div>
  );
};

export default function AddProductPage() {
  const navigate = useNavigate();
  const generateAppPath = useAppPath();
  const productService = useInjection<ProductService>(ProductService);
  const [loading, setLoading] = useState(false);

  const { handleSubmit, control } = useForm({
    resolver: zodResolver(schema),
    mode: "onChange",
  });

  const handleInventoryNav = () => {
    navigate(generateAppPath(AppPaths.inventory));
  };

  const transformPayload = (input: InputPayload): OutputPayload => {
    const {
      title,
      description,
      price,
      salePrice,
      per_item_quantity,
      eventTitleAR,
      category,
      maxAttendees,
      totalEventGuests,
      ...rest
    } = input;

    // Mapping keys to labels for custom_attributes
    const customAttributeMap: Record<string, string> = {
      communityId: "community-id",
      eventCommunityAR: "eventCommunityAr",
      eventCommunityEN: "eventCommunityEn",
      eventContactNumber: "eventContactNumber",
      eventDate: "eventDate",
      endDate: "end-date",
      eventDescriptionAR: "eventDescriptionAr",
      eventDescriptionEN: "eventDescriptionEn",
      eventRegistrationStatus: "eventRegistrationStatus",
      eventTermsAndConditionsAR: "tnrAr",
      eventTermsAndConditionsEN: "tnrEn",
      eventTitleAR: "eventTitleAr",
      eventTitleEN: "eventTitleEn",
      locationLink: "location-link",
      maxAttendees: "maxAttendees",
      totalEventGuests: "totalEventGuests",
    };

    const custom_attributes: CustomAttribute[] = Object.entries(rest)
      .filter(([key]) => key in customAttributeMap)
      .map(([key, value]) => ({
        label: customAttributeMap[key],
        value,
      }));

    return {
      title,
      description: description || "",
      weight_unit: "g",
      weight: "",
      price,
      sale_price: salePrice,
      categories: [],
      images: [],
      variants: [],
      modifier_groups: [],
      combinations: [],
      is_popular: false,
      cross_sell_groups: [],
      title_ar: eventTitleAR,
      marketplace_categories: [24],
      per_item_quantity: 123,
      stock_quantity: null,
      sku: "",
      custom_attributes,
      status: "ACTIVE",
      manage_stock: false,
    };
  };

  const transformAndSubmit = async (input: InputPayload) => {
    setLoading(true);
    const output = transformPayload(input);
    await productService.createProduct(output);
    setLoading(false);
    navigate(generateAppPath(AppPaths.inventory));
  };

  return (
    <div css={styles.wrapper}>
      <RDSButton
        css={styles.button}
        variant="tertiary"
        size="lg"
        text="Back to inventories"
        leadIcon="left_arrow"
        onClick={handleInventoryNav}
      />
      <div css={styles.sectionsWrapper}>
        <form css={[styles.form, styles.sectionLayouts]} onSubmit={handleSubmit((args) => {})}>
          <Section heading="INVENTORY DETAILS">
            <RDSUploadFile label="Gallery" />
            <Input name="title" control={control} label="Title" isRequired placeholder="Title..." />
            <Input
              name="description"
              control={control}
              label="Description"
              isRequired
              placeholder="description..."
            />
            <Select
              control={control}
              name="category"
              label="Category"
              data-testid="category"
              isRequired
              placeholder="Select a category..."
              options={[
                { label: "Villa", value: "villa" },
                { label: "Town House", value: "town-house" },
                { label: "Duplex", value: "duplex" },
              ]}
            />
            <Input
              name="price"
              control={control}
              label="Price"
              isRequired
              placeholder="Enter price..."
            />
            <Input
              name="salePrice"
              control={control}
              label="Sale Price"
              isRequired
              placeholder="Enter sale price..."
            />
            <Input
              name="per_item_quantity"
              control={control}
              label="Product Unit"
              isRequired
              placeholder="Enter product unit..."
            />
            <RDSButton
              variant="primary"
              size="lg"
              text="Save Changes"
              onClick={handleSubmit((args) => {
                transformAndSubmit(args);
              })}
              loading={loading}
            />
          </Section>
          {/* <Section heading="Custom Attributes"> */}
          {/* <Input
              name="communityId"
              control={control}
              label="Community ID"
              isRequired
              placeholder=""
            />
            <Input
              name="eventCommunityAR"
              control={control}
              label="Event Community AR"
              isRequired
              placeholder=""
            />
            <Input
              name="eventCommunityEN"
              control={control}
              label="Event Community EN"
              isRequired
              placeholder=""
            />
            <Input
              name="eventContactNumber"
              control={control}
              label="Event Contact Number"
              isRequired
              placeholder=""
            />
            <Input
              name="eventDescriptionAR"
              control={control}
              label="Event Description AR"
              isRequired
              placeholder=""
            />
            <Input
              name="eventDescriptionEN"
              control={control}
              label="Event Description EN"
              isRequired
              placeholder=""
            />
            <Input
              name="eventRegistrationStatus"
              control={control}
              label="Event Registration Status"
              isRequired
              placeholder=""
            />
            <Input
              name="eventTermsAndConditionsAR"
              control={control}
              label="Event Terms And Conditions AR"
              isRequired
              placeholder=""
            />
            <Input
              name="eventTermsAndConditionsEN"
              control={control}
              label="Event Terms And Conditions EN"
              isRequired
              placeholder=""
            />
            <Input
              name="eventTitleEN"
              control={control}
              label="Event Title EN"
              isRequired
              placeholder=""
            />
            <Input
              name="eventTitleAR"
              control={control}
              label="Event Title AR"
              isRequired
              placeholder=""
            />
            <Input
              name="locationLink"
              control={control}
              label="Location Link"
              isRequired
              placeholder=""
            />
            <Input
              name="maxAttendees"
              control={control}
              label="Max Attendees"
              isRequired
              placeholder=""
            />
            <Input
              name="totalEventGuests"
              control={control}
              label="Total Event Guests"
              isRequired
              placeholder=""
            /> */}
          {/* <RDSButton
              variant="primary"
              size="lg"
              text="Save Changes"
              onClick={handleSubmit((args) => {
                transformAndSubmit(args);
              })}
              loading={loading}
            /> */}
          {/* </Section> */}
        </form>
      </div>
    </div>
  );
}
