import { Container } from "inversify";
import { ProjectService } from "~/interface/projectTypes";

export async function getProjectService(container: Container): Promise<ProjectService> {
  let projectServicePromise: Promise<ProjectService> | null = null;

  if (container.isBound(ProjectService)) {
    return container.get(ProjectService);
  }

  if (!projectServicePromise) {
    projectServicePromise = import("./project-impl").then(({ ProjectServiceImpl }) => {
      container.bind<ProjectService>(ProjectService).to(ProjectServiceImpl).inSingletonScope();

      return container.get(ProjectService);
    });
  }

  return projectServicePromise;
}
