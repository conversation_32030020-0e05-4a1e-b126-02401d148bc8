import { Container } from "inversify";
import { ProjectService } from "./project";


export async function getProjectService(service: any, container: Container): Promise<> {
let projectServicePromise: Promise<> | null = null;

	if (container.isBound(service)) {
		return container.get(service);
	}

	if (!projectServicePromise) {
		projectServicePromise = import(
			"./project-impl"
		).then(({ ProjectServiceImpl }) => {
			container
				.bind<>(service)
				.to(ProjectServiceImpl);

			return container.get(service);
		});
	}

	return projectServicePromise;
}
