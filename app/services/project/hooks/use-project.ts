import { useQuery } from "@tanstack/react-query";

import { QueryKey } from "~/context/reactQueryProvider";
import { useProjectService } from "./use-project-service";
import { useDIContainer } from "~/hooks/use-di";
import { getProjectService } from "../get-project-service";

export function useProjectList(args: { limit: number; offset: number; }) {
  const container = useDIContainer();
  getProjectService(container);
  const projectService = getProjectService(container);
  
  return useQuery({
    queryKey: [QueryKey.PROJECT_LIST, args],
    queryFn: () => projectService?.getProjectList(args),
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
    enabled: projectService !== null,
  });
}