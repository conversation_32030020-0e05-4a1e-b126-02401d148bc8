import { useQuery } from "@tanstack/react-query";
import { QueryKey } from "~/context/reactQueryProvider";
import { useDIContainer } from "~/hooks/use-di";
import { getProjectService } from "../get-project-service";
import { ProjectService } from "../project";

export function useProjectList(args: { limit: number; offset: number; }) {
  const container = useDIContainer();

  return useQuery({
    queryKey: [QueryKey.PROJECT_LIST, args],
    queryFn: async () => {
      const projectService = await getProjectService(ProjectService, container);
      return projectService.getProjectList(args);
    },
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });
}