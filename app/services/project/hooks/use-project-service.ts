import { useEffect, useState } from "react";
import { useDIContainer } from "~/hooks/use-di";
import { ProjectService } from "../project";
import { getProjectService } from "../get-project-service";

export function useProjectService(): ProjectService | null {
	const container = useDIContainer();
	const [service, setService] = useState<ProjectService | null>(null);

	useEffect(() => {
		getProjectService(container).then(setService);
	}, [container]);

	return service;
}
